using Microsoft.Extensions.Logging;
using System.IO.Compression;

namespace SmaTrendFollower.Services;

/// <summary>
/// Simple slippage model trainer that creates a basic ATR-multiplier model
/// </summary>
internal sealed class SlippageModelTrainer : ISlippageModelTrainer
{
    private readonly ILogger<SlippageModelTrainer> _log;

    public SlippageModelTrainer(ILogger<SlippageModelTrainer> log) => _log = log;

    public async Task<bool> TrainAsync(CancellationToken ct = default)
    {
        try
        {
            _log.LogInformation("Training slippage model (simple ATR-multiplier) …");
            
            // Very naive placeholder: write JSON + zip it.
            var tmpDir = Path.Combine(Path.GetTempPath(), "slippage_tmp");
            Directory.CreateDirectory(tmpDir);
            var json = Path.Combine(tmpDir, "model.json");
            await File.WriteAllTextAsync(json, """{ "version":1, "atrMultiplier":0.25 }""", ct);

            var outZip = Path.Combine("Model", "slippage_model.zip");
            Directory.CreateDirectory("Model");
            if (File.Exists(outZip)) File.Delete(outZip);
            ZipFile.CreateFromDirectory(tmpDir, outZip);
            Directory.Delete(tmpDir, true);

            _log.LogInformation("slippage_model.zip created ({Bytes} bytes)", new FileInfo(outZip).Length);
            return true;
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "Slippage model training failed");
            return false;
        }
    }
}
