using System.Collections.Concurrent;
using StackExchange.Redis;
using Prometheus;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time quote volatility guard that monitors price volatility using a time-based rolling window.
/// Automatically halts trading for symbols showing excessive price volatility (z-score > threshold)
/// to prevent execution during market disruptions or quote instability.
///
/// Uses a configurable time-based rolling window to detect price anomalies with configurable sigma threshold.
/// Time-based windowing handles variable tick frequencies better than count-based windows.
/// </summary>
public sealed class QuoteVolatilityGuard : IDisposable
{
    private readonly ConcurrentDictionary<string, ConcurrentQueue<(DateTime ts, decimal px)>> _symbolTicks = new();
    private readonly IDatabase? _redis;
    private readonly int _windowSeconds;
    private readonly double _sigmaThreshold;
    private readonly TimeSpan _haltDuration;
    private readonly ILogger<QuoteVolatilityGuard> _log;
    private bool _disposed;

    // Prometheus metrics - keeping existing metric name for compatibility
    private static readonly Counter QuoteGuardHalts =
        Metrics.CreateCounter("quote_guard_halts_total", "Total quote volatility guard halts",
            new CounterConfiguration { LabelNames = new[] { "symbol" } });

    public QuoteVolatilityGuard(QuoteVolatilityOptions opt, ILogger<QuoteVolatilityGuard> log, IConnectionMultiplexer? connectionMultiplexer = null)
    {
        _windowSeconds   = opt.WindowSeconds;
        _sigmaThreshold  = opt.StdDevSigma;
        _haltDuration    = TimeSpan.FromSeconds(opt.HaltDurationSeconds);
        _log             = log;
        _redis           = connectionMultiplexer?.GetDatabase();

        if (_redis == null)
        {
            _log.LogWarning("Redis not available, QuoteVolatilityGuard will operate in degraded mode (in-memory only)");
        }

        _log.LogInformation("QuoteVolatilityGuard initialized with window={WindowSeconds}s, threshold={Threshold}σ, halt={HaltDuration}s",
            _windowSeconds, _sigmaThreshold, _haltDuration.TotalSeconds);
    }

    // Constructor for backward compatibility with tests
    public QuoteVolatilityGuard(IConnectionMultiplexer? connectionMultiplexer, ILogger<QuoteVolatilityGuard> log)
    {
        _windowSeconds   = 120;
        _sigmaThreshold  = 2.0;
        _haltDuration    = TimeSpan.FromMinutes(2);
        _log             = log;
        _redis           = connectionMultiplexer?.GetDatabase();

        if (_redis == null)
        {
            _log.LogWarning("Redis not available, QuoteVolatilityGuard will operate in degraded mode (in-memory only)");
        }

        _log.LogInformation("QuoteVolatilityGuard initialized with window={WindowSeconds}s, threshold={Threshold}σ, halt={HaltDuration}m",
            _windowSeconds, _sigmaThreshold, _haltDuration.TotalMinutes);
    }

    /// <summary>
    /// Processes a price update and checks for volatility anomalies
    /// </summary>
    /// <param name="price">Current price</param>
    public void OnQuote(decimal price)
    {
        OnQuote("DEFAULT", (price + price) / 2, (price + price) / 2); // Convert to symbol-based call
    }

    /// <summary>
    /// Processes a quote update and checks for volatility anomalies
    /// </summary>
    /// <param name="symbol">Trading symbol</param>
    /// <param name="bid">Bid price</param>
    /// <param name="ask">Ask price</param>
    public void OnQuote(string symbol, decimal bid, decimal ask)
    {
        if (string.IsNullOrEmpty(symbol) || bid <= 0 || ask <= 0 || ask <= bid)
        {
            return; // Skip invalid quotes
        }

        try
        {
            var midPrice = (bid + ask) / 2;
            var now = DateTime.UtcNow;

            // Get or create tick queue for this symbol
            var ticks = _symbolTicks.GetOrAdd(symbol, _ => new ConcurrentQueue<(DateTime ts, decimal px)>());
            ticks.Enqueue((now, midPrice));

            // purge old ticks
            while (ticks.TryPeek(out var head) && (now - head.ts).TotalSeconds > _windowSeconds)
                ticks.TryDequeue(out _);

            // Check for volatility anomaly
            if (IsVolatile(ticks))
            {
                _ = Task.Run(async () => await HaltTradingAsync(symbol));
            }
        }
        catch (Exception ex)
        {
            _log.LogWarning(ex, "Error processing quote volatility for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Checks if current price volatility exceeds the threshold
    /// </summary>
    /// <param name="ticks">Tick queue to analyze</param>
    /// <returns>True if volatility is excessive, false otherwise</returns>
    private bool IsVolatile(ConcurrentQueue<(DateTime ts, decimal px)> ticks)
    {
        if (ticks.Count < 20) return false;              // need some data
        var px = ticks.Select(t => t.px).ToArray();
        var mean  = (double)px.Average(v => v);
        var sigma = Math.Sqrt(px.Sum(v => Math.Pow((double)(v - (decimal)mean), 2)) / px.Length);

        return sigma > _sigmaThreshold * mean / 100.0;   // threshold is % of price
    }

    /// <summary>
    /// Halts trading for a symbol due to quote volatility anomaly
    /// </summary>
    private async Task HaltTradingAsync(string symbol)
    {
        try
        {
            if (_redis != null)
            {
                var haltKey = $"halt:{symbol}";
                await _redis.StringSetAsync(haltKey, "volatility", _haltDuration);
            }

            // Update metrics
            QuoteGuardHalts.WithLabels(symbol).Inc();

            _log.LogWarning("Quote volatility anomaly detected: {Symbol} - trading halted for {Duration}s",
                symbol, _haltDuration.TotalSeconds);
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "Failed to halt trading for {Symbol} after quote volatility anomaly", symbol);
        }
    }

    /// <summary>
    /// Checks if trading is currently halted for a symbol due to quote volatility
    /// </summary>
    /// <param name="symbol">Trading symbol to check</param>
    /// <returns>True if trading is halted due to quote volatility, false otherwise</returns>
    public async Task<bool> IsTradingHaltedAsync(string symbol)
    {
        if (string.IsNullOrEmpty(symbol))
            return false;

        try
        {
            if (_redis == null)
            {
                return false; // No Redis available, allow trading
            }

            var haltKey = $"halt:{symbol}";
            var haltValue = await _redis.StringGetAsync(haltKey);
            return haltValue.HasValue && haltValue == "volatility";
        }
        catch (Exception ex)
        {
            _log.LogWarning(ex, "Error checking quote volatility halt status for {Symbol}", symbol);
            return false; // Default to allowing trading if Redis is unavailable
        }
    }

    /// <summary>
    /// Gets current spread statistics for a symbol (simplified for compatibility)
    /// </summary>
    /// <param name="symbol">Trading symbol</param>
    /// <returns>Spread statistics snapshot or null if no data</returns>
    public (int Count, int WindowSize, bool HasSufficientData)? GetSpreadStats(string symbol)
    {
        if (string.IsNullOrEmpty(symbol))
            return null;

        if (_symbolTicks.TryGetValue(symbol, out var ticks))
        {
            var count = ticks.Count;
            return (count, _windowSeconds, count >= 20);
        }

        return null;
    }

    /// <summary>
    /// Clears spread statistics for a symbol (useful for testing)
    /// </summary>
    /// <param name="symbol">Trading symbol</param>
    public void ClearSpreadStats(string symbol)
    {
        if (!string.IsNullOrEmpty(symbol) && _symbolTicks.TryRemove(symbol, out _))
        {
            _log.LogDebug("Cleared spread statistics for {Symbol}", symbol);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _symbolTicks.Clear();

        _log.LogInformation("QuoteVolatilityGuard disposed");
    }
}
