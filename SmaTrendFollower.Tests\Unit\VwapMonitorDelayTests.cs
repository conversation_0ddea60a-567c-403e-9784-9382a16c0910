using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using Xunit;

namespace SmaTrendFollower.Tests.Unit;

/// <summary>
/// Tests for VWAP Monitor delay functionality to avoid pre-market skew
/// </summary>
public class VwapMonitorDelayTests : IDisposable
{
    private readonly Mock<ITickStreamService> _mockTickStreamService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IMarketRegimeService> _mockMarketRegimeService;
    private readonly Mock<ILogger<VWAPMonitorService>> _mockLogger;
    private readonly IConfiguration _configuration;
    private readonly VwapOptions _vwapOptions;
    private VWAPMonitorService? _vwapMonitorService;

    public VwapMonitorDelayTests()
    {
        _mockTickStreamService = new Mock<ITickStreamService>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockMarketRegimeService = new Mock<IMarketRegimeService>();
        _mockLogger = new Mock<ILogger<VWAPMonitorService>>();
        
        // Create test configuration
        var configBuilder = new ConfigurationBuilder();
        configBuilder.AddInMemoryCollection(new Dictionary<string, string?>
        {
            ["VWAPMonitor:RollingMinutes"] = "30",
            ["VWAPMonitor:MinTradesRequired"] = "10",
            ["VWAPMonitor:EnableVWAPFilter"] = "true"
        });
        _configuration = configBuilder.Build();
        
        // Create VWAP options for 9:40 AM start time
        _vwapOptions = new VwapOptions { StartHour = 9, StartMinute = 40 };
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void VwapMonitorService_ShouldInitializeWithCorrectOptions()
    {
        // Arrange
        var optionsWrapper = Options.Create(_vwapOptions);

        // Act
        _vwapMonitorService = new VWAPMonitorService(
            _mockTickStreamService.Object,
            _mockRedisService.Object,
            _mockMarketRegimeService.Object,
            _configuration,
            optionsWrapper,
            _mockLogger.Object);

        // Assert
        _vwapMonitorService.Should().NotBeNull();
        _vwapMonitorService.GetStatus().Should().Be(VWAPMonitorStatus.Stopped);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public async Task StartMonitoringAsync_OnWeekend_ShouldStartImmediately()
    {
        // Arrange
        var optionsWrapper = Options.Create(_vwapOptions);
        _vwapMonitorService = new VWAPMonitorService(
            _mockTickStreamService.Object,
            _mockRedisService.Object,
            _mockMarketRegimeService.Object,
            _configuration,
            optionsWrapper,
            _mockLogger.Object);

        var symbols = new[] { "AAPL" };
        var startTime = DateTime.UtcNow;

        // Act
        await _vwapMonitorService.StartMonitoringAsync(symbols);

        // Assert
        var endTime = DateTime.UtcNow;
        var elapsed = endTime - startTime;
        
        // Should complete quickly (within 1 second) on weekends since no delay is needed
        elapsed.Should().BeLessThan(TimeSpan.FromSeconds(1));
        _vwapMonitorService.GetStatus().Should().Be(VWAPMonitorStatus.Active);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void VwapOptions_ShouldLoadCorrectDefaults()
    {
        // Arrange & Act
        var options = new VwapOptions();

        // Assert
        options.StartHour.Should().Be(9);
        options.StartMinute.Should().Be(40);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void VwapOptions_ShouldLoadFromConfiguration()
    {
        // Arrange
        var configBuilder = new ConfigurationBuilder();
        configBuilder.AddInMemoryCollection(new Dictionary<string, string?>
        {
            ["VWAP:StartHour"] = "10",
            ["VWAP:StartMinute"] = "15"
        });
        var configuration = configBuilder.Build();

        // Act
        var vwapSection = configuration.GetSection("VWAP");
        var options = new VwapOptions();
        vwapSection.Bind(options);

        // Assert
        options.StartHour.Should().Be(10);
        options.StartMinute.Should().Be(15);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public async Task StartMonitoringAsync_WithEmptySymbols_ShouldReturnImmediately()
    {
        // Arrange
        var optionsWrapper = Options.Create(_vwapOptions);
        _vwapMonitorService = new VWAPMonitorService(
            _mockTickStreamService.Object,
            _mockRedisService.Object,
            _mockMarketRegimeService.Object,
            _configuration,
            optionsWrapper,
            _mockLogger.Object);

        var symbols = Array.Empty<string>();
        var startTime = DateTime.UtcNow;

        // Act
        await _vwapMonitorService.StartMonitoringAsync(symbols);

        // Assert
        var endTime = DateTime.UtcNow;
        var elapsed = endTime - startTime;
        
        elapsed.Should().BeLessThan(TimeSpan.FromMilliseconds(100));
        _vwapMonitorService.GetStatus().Should().Be(VWAPMonitorStatus.Stopped);
    }

    public void Dispose()
    {
        _vwapMonitorService?.Dispose();
    }
}
